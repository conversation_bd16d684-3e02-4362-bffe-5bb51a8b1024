# Excel转CSV转换脚本
# 将目录中的所有Excel文件转换为CSV格式

param(
    [string]$InputPath = ".",
    [string]$OutputPath = "csv_output"
)

Write-Host "========================================" -ForegroundColor Green
Write-Host "Excel转CSV转换工具" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# 创建输出目录
if (!(Test-Path $OutputPath)) {
    New-Item -ItemType Directory -Path $OutputPath | Out-Null
    Write-Host "创建输出目录: $OutputPath" -ForegroundColor Yellow
}

# 查找所有Excel文件
$excelFiles = Get-ChildItem -Path $InputPath -Recurse -Include "*.xlsx", "*.xls" | Where-Object { !$_.Name.StartsWith("~$") }

if ($excelFiles.Count -eq 0) {
    Write-Host "未找到Excel文件" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit
}

Write-Host "找到 $($excelFiles.Count) 个Excel文件" -ForegroundColor Cyan
Write-Host ""

# 尝试创建Excel应用程序对象
try {
    $excel = New-Object -ComObject Excel.Application
    $excel.Visible = $false
    $excel.DisplayAlerts = $false
    Write-Host "Excel应用程序已启动" -ForegroundColor Green
} catch {
    Write-Host "错误: 无法启动Excel应用程序" -ForegroundColor Red
    Write-Host "请确保已安装Microsoft Excel" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit
}

$successCount = 0
$failedFiles = @()

foreach ($file in $excelFiles) {
    try {
        Write-Host "正在转换: $($file.Name)" -ForegroundColor Yellow
        
        # 打开Excel文件
        $workbook = $excel.Workbooks.Open($file.FullName)
        
        # 获取活动工作表
        $worksheet = $workbook.ActiveSheet
        
        # 生成CSV文件名
        $csvFileName = [System.IO.Path]::GetFileNameWithoutExtension($file.Name) + ".csv"
        $csvPath = Join-Path $OutputPath $csvFileName
        
        # 保存为CSV格式 (xlCSVUTF8 = 62)
        $worksheet.SaveAs($csvPath, 62)
        
        # 关闭工作簿
        $workbook.Close($false)
        
        Write-Host "✓ 成功转换: $csvFileName" -ForegroundColor Green
        $successCount++
        
    } catch {
        Write-Host "✗ 转换失败: $($file.Name) - $($_.Exception.Message)" -ForegroundColor Red
        $failedFiles += $file.Name
        
        # 尝试关闭可能打开的工作簿
        try {
            if ($workbook) {
                $workbook.Close($false)
            }
        } catch {}
    }
}

# 关闭Excel应用程序
try {
    $excel.Quit()
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
} catch {}

Write-Host ""
Write-Host "转换完成！" -ForegroundColor Green
Write-Host "成功: $successCount 个文件" -ForegroundColor Green
Write-Host "失败: $($failedFiles.Count) 个文件" -ForegroundColor $(if ($failedFiles.Count -gt 0) { "Red" } else { "Green" })

if ($failedFiles.Count -gt 0) {
    Write-Host ""
    Write-Host "失败的文件:" -ForegroundColor Red
    foreach ($failedFile in $failedFiles) {
        Write-Host "  - $failedFile" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "CSV文件已保存到: $OutputPath" -ForegroundColor Cyan
Write-Host "现在可以运行 convert_csv_only.bat 来转换为WooCommerce格式" -ForegroundColor Cyan
Write-Host ""

Read-Host "按任意键退出"
