# WooCommerce产品数据转换工具 - 使用说明

## 📋 工具概述

这套工具可以智能地将您的产品数据转换成WooCommerce兼容的CSV格式，支持：

✅ **智能字段识别** - 自动识别不同语言的字段名称  
✅ **HTML标签优化** - 清理和转换HTML描述  
✅ **分隔符标准化** - 统一分类、标签、图片等字段的分隔符  
✅ **数据验证** - 跳过不完整或重复的产品  
✅ **批量处理** - 一次处理多个文件  
✅ **自动分割** - 每个CSV文件最多30,000个产品  

## 🎯 支持的数据格式

### 输入格式
- Excel文件 (.xlsx, .xls)
- CSV文件 (.csv)

### 输出格式
- WooCommerce标准CSV格式
- UTF-8编码，支持中文等多语言

## 🚀 使用方法

### 🔥 推荐方法：一键转换
```
双击运行：convert_all.bat
```
这个方法会：
1. 自动检测Excel文件并转换为CSV
2. 将所有CSV文件转换为WooCommerce格式
3. 自动处理所有子目录中的文件

### 📁 仅处理CSV文件
如果您已经有CSV文件：
```
双击运行：convert_csv_only.bat
```

### 📊 仅转换Excel为CSV
如果您只想将Excel转换为CSV：
```
右键 -> 使用PowerShell运行：excel_to_csv.ps1
```

## 📝 字段映射说明

工具会自动识别以下字段（支持中文、英文、西班牙文、意大利文等）：

| WooCommerce字段 | 识别的字段名称示例 |
|----------------|------------------|
| **产品名称** | name, title, nombre, nome, 产品名称, 商品名称 |
| **描述** | description, descripcion, 描述, 产品描述 |
| **价格** | price, precio, prezzo, 价格, 售价 |
| **分类** | category, categoria, 分类, 类别 |
| **标签** | tags, etiquetas, 标签, 关键词 |
| **图片** | images, imagen, 图片, 图像 |
| **品牌** | brand, marca, 品牌, 厂商 |
| **SKU** | sku, code, codigo, 编码, 货号 |
| **库存** | stock, quantity, 库存, 数量 |

## 🔧 数据处理规则

### HTML描述优化
- 自动清理无用的HTML标签
- 转换 `<b>` → `<strong>`
- 转换 `<i>` → `<em>`
- 解码HTML实体字符

### 分隔符标准化
- **分类**: 使用 ` > ` 分隔（如：`电子产品 > 手机 > 智能手机`）
- **标签**: 使用 `, ` 分隔（如：`热销, 新品, 推荐`）
- **图片**: 使用 `, ` 分隔多个图片URL

### 数据验证
自动跳过以下情况：
- 缺少产品名称或价格
- 价格格式错误或为0
- SKU重复的产品
- 完全空白的行

## 📊 输出文件说明

### 文件命名规则
- **单个文件**: `原文件名_wc.csv`
- **多个文件**: `原文件名_wc_part1.csv`, `原文件名_wc_part2.csv`, ...

### 文件大小控制
- 每个CSV文件最多包含30,000个产品
- 超出会自动分割成多个文件
- 确保导入WooCommerce时不会超时

## ⚠️ 注意事项

### 数据准备
1. **图片URL**: 确保是完整的网址（如：`https://example.com/image.jpg`）
2. **价格格式**: 应该是数字格式，支持小数点
3. **分类层级**: 建议不超过4级
4. **文件编码**: 支持UTF-8、GBK等多种编码

### 系统要求
- Windows系统
- Python 3.x（工具会自动检测）
- 对于Excel文件：需要安装Microsoft Excel

## 🐛 故障排除

### 常见问题

**Q: 提示"未找到Python"**  
A: 请安装Python 3.x，下载地址：https://python.org

**Q: Excel文件转换失败**  
A: 确保安装了Microsoft Excel，或手动将Excel文件另存为CSV格式

**Q: 字段识别不准确**  
A: 检查字段名称是否包含支持的关键词，可以手动调整字段名称

**Q: 价格字段错误**  
A: 确保价格字段只包含数字和小数点，移除货币符号

**Q: 图片链接显示异常**  
A: 确保图片URL是完整的网址，多个图片用逗号分隔

### 日志信息
运行时会显示详细信息：
- 检测到的字段映射
- 处理的产品数量
- 跳过的产品及原因
- 生成的文件信息

## 📈 使用示例

### 输入数据示例
```csv
name,description,price,category,brand,sku,stock,images
"智能手机","<b>高性能</b>智能手机","999.99","电子产品>手机","Apple","IP14-128","100","https://example.com/phone.jpg"
```

### 输出结果
转换后会生成完整的WooCommerce格式CSV文件，可以直接导入到WooCommerce商店。

## 📞 技术支持

如果遇到问题：
1. 检查日志输出中的错误信息
2. 确认数据格式是否正确
3. 查看生成的示例文件格式

---

**提示**: 建议先用少量数据测试转换效果，确认无误后再处理大批量数据。
