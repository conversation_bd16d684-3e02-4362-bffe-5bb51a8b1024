#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WooCommerce产品数据转换工具
智能转换各种格式的产品数据到WooCommerce兼容的CSV格式
"""

import pandas as pd
import re
import os
import sys
from pathlib import Path
import html
from bs4 import BeautifulSoup
import argparse
import logging
import json

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WooCommerceConverter:
    def __init__(self, config_file='field_mapping_config.json'):
        # WooCommerce标准字段映射
        self.wc_columns = [
            'ID', 'Type', 'SKU', 'Name', 'Published', 'Is featured?', 'Visibility in catalog',
            'Short description', 'Description', 'Date sale price starts', 'Date sale price ends',
            'Tax status', 'Tax class', 'In stock?', 'Stock', 'Low stock amount', 'Backorders allowed?',
            'Sold individually?', 'Weight (kg)', 'Length (cm)', 'Width (cm)', 'Height (cm)',
            'Allow customer reviews?', 'Purchase note', 'Sale price', 'Regular price', 'Categories',
            'Tags', 'Shipping class', 'Images', 'Download limit', 'Download expiry days', 'Parent',
            'Grouped products', 'Upsells', 'Cross-sells', 'External URL', 'Button text', 'Position',
            'Attribute 1 name', 'Attribute 1 value(s)', 'Attribute 1 visible', 'Attribute 1 global',
            'Attribute 2 name', 'Attribute 2 value(s)', 'Attribute 2 visible', 'Attribute 2 global',
            'Attribute 3 name', 'Attribute 3 value(s)', 'Attribute 3 visible', 'Attribute 3 global',
            'Attribute 4 name', 'Attribute 4 value(s)', 'Attribute 4 visible', 'Attribute 4 global',
            'Brands', 'Meta: rank_math_focus_keyword'
        ]

        # 加载配置文件
        self.load_config(config_file)

    def load_config(self, config_file):
        """加载配置文件"""
        try:
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                self.field_mappings = config.get('field_mappings', {})
                self.separator_mappings = config.get('separator_mappings', {})
                self.html_tag_replacements = config.get('html_tag_replacements', {})
                self.required_fields = config.get('required_fields', ['name', 'regular_price'])
                self.default_values = config.get('default_values', {})

                logger.info(f"已加载配置文件: {config_file}")
            else:
                logger.warning(f"配置文件不存在: {config_file}，使用默认配置")
                self.use_default_config()

        except Exception as e:
            logger.error(f"加载配置文件失败: {e}，使用默认配置")
            self.use_default_config()

    def use_default_config(self):
        """使用默认配置"""
        # 常见字段映射规则
        self.field_mappings = {
            # 产品名称相关
            'name': ['name', 'title', 'product_name', 'nombre', 'nome', 'titel', 'nom'],
            'description': ['description', 'desc', 'descripcion', 'descrizione', 'beschreibung'],
            'short_description': ['short_description', 'short_desc', 'summary', 'resumen'],

            # 价格相关
            'regular_price': ['price', 'regular_price', 'precio', 'prezzo', 'preis', 'prix'],
            'sale_price': ['sale_price', 'offer_price', 'precio_oferta', 'prezzo_scontato'],

            # 库存相关
            'stock': ['stock', 'quantity', 'qty', 'cantidad', 'quantita', 'menge'],
            'sku': ['sku', 'code', 'codigo', 'codice', 'artikel'],

            # 分类和标签
            'categories': ['category', 'categories', 'categoria', 'categorie', 'kategorie'],
            'tags': ['tags', 'etiquetas', 'tag', 'schlagworte'],

            # 图片
            'images': ['image', 'images', 'imagen', 'immagine', 'bild', 'photo'],

            # 品牌
            'brand': ['brand', 'marca', 'marque', 'marke'],

            # 重量和尺寸
            'weight': ['weight', 'peso', 'gewicht', 'poids'],
            'length': ['length', 'largo', 'lunghezza', 'lange'],
            'width': ['width', 'ancho', 'larghezza', 'breite'],
            'height': ['height', 'alto', 'altezza', 'hohe']
        }

        self.separator_mappings = {
            'categories': ' > ',
            'tags': ', ',
            'images': ', '
        }

        self.html_tag_replacements = {
            'b': 'strong',
            'i': 'em',
            'u': 'span style="text-decoration: underline;"'
        }

        self.required_fields = ['name', 'regular_price']

        self.default_values = {
            'Type': 'simple',
            'Published': '1',
            'Is featured?': '0',
            'Visibility in catalog': 'visible',
            'Tax status': 'taxable',
            'In stock?': '1',
            'Backorders allowed?': '0',
            'Sold individually?': '0',
            'Allow customer reviews?': '1',
            'Position': '0'
        }
        
    def clean_html_description(self, text):
        """清理和优化HTML描述"""
        if pd.isna(text) or not text:
            return ""
            
        # 转换为字符串
        text = str(text)
        
        # 解码HTML实体
        text = html.unescape(text)
        
        # 使用BeautifulSoup清理HTML
        soup = BeautifulSoup(text, 'html.parser')
        
        # 移除不需要的标签
        for tag in soup(['script', 'style', 'meta', 'link']):
            tag.decompose()
            
        # 转换常见标签为WooCommerce兼容格式
        tag_replacements = self.html_tag_replacements
        
        for old_tag, new_tag in tag_replacements.items():
            for tag in soup.find_all(old_tag):
                tag.name = new_tag.split()[0] if ' ' in new_tag else new_tag
                if ' ' in new_tag:
                    attrs = new_tag.split(' ', 1)[1]
                    # 简单解析属性
                    if 'style=' in attrs:
                        style_value = attrs.split('style="')[1].split('"')[0]
                        tag['style'] = style_value
        
        # 清理空白和格式
        cleaned_text = str(soup)
        cleaned_text = re.sub(r'\s+', ' ', cleaned_text)
        cleaned_text = cleaned_text.strip()
        
        return cleaned_text
    
    def normalize_separators(self, text, field_type='default', target_separator='|'):
        """标准化分隔符"""
        if pd.isna(text) or not text:
            return ""

        text = str(text)

        # 根据字段类型获取目标分隔符
        if field_type in self.separator_mappings:
            target_separator = self.separator_mappings[field_type]

        # 常见分隔符列表
        separators = [',', ';', '/', '\\', '|', '>', '<', '&gt;', '&lt;', ' > ', ' / ', ' | ']

        # 替换所有分隔符为目标分隔符
        for sep in separators:
            if sep != target_separator:
                text = text.replace(sep, target_separator)

        # 清理多余的分隔符和空白
        if target_separator == ' > ':
            text = re.sub(r'\s*>\s*', ' > ', text)
            text = re.sub(r'(\s*>\s*)+', ' > ', text)
        elif target_separator == ', ':
            text = re.sub(r'\s*,\s*', ', ', text)
            text = re.sub(r'(,\s*)+', ', ', text)
        else:
            text = re.sub(f'\\{target_separator}+', target_separator, text)

        text = text.strip().strip(target_separator.strip())

        return text
    
    def detect_field_mapping(self, df):
        """智能检测字段映射"""
        columns = [col.lower().strip() for col in df.columns]
        mapping = {}
        
        for wc_field, possible_names in self.field_mappings.items():
            for col in columns:
                for possible_name in possible_names:
                    if possible_name.lower() in col or col in possible_name.lower():
                        original_col = df.columns[columns.index(col)]
                        mapping[wc_field] = original_col
                        break
                if wc_field in mapping:
                    break
        
        logger.info(f"检测到的字段映射: {mapping}")
        return mapping
    
    def validate_product_row(self, row, mapping):
        """验证产品行是否完整"""
        # 必需字段检查
        required_fields = self.required_fields
        
        for field in required_fields:
            if field in mapping:
                col = mapping[field]
                if pd.isna(row[col]) or not str(row[col]).strip():
                    return False, f"缺少必需字段: {field}"
        
        # 价格验证
        if 'regular_price' in mapping:
            try:
                price = str(row[mapping['regular_price']]).replace(',', '.')
                price = re.sub(r'[^\d.]', '', price)
                if not price or float(price) <= 0:
                    return False, "价格无效"
            except:
                return False, "价格格式错误"
        
        return True, "有效"

    def convert_to_woocommerce(self, input_file, output_dir=None, max_products_per_file=30000):
        """转换产品数据到WooCommerce格式"""
        logger.info(f"开始处理文件: {input_file}")

        # 读取输入文件
        try:
            if input_file.endswith('.xlsx') or input_file.endswith('.xls'):
                df = pd.read_excel(input_file)
            elif input_file.endswith('.csv'):
                # 尝试不同的编码
                encodings = ['utf-8', 'gbk', 'gb2312', 'latin1', 'cp1252']
                df = None
                for encoding in encodings:
                    try:
                        df = pd.read_csv(input_file, encoding=encoding)
                        logger.info(f"成功使用编码 {encoding} 读取文件")
                        break
                    except:
                        continue
                if df is None:
                    raise Exception("无法读取CSV文件，请检查编码格式")
            else:
                raise Exception("不支持的文件格式")

        except Exception as e:
            logger.error(f"读取文件失败: {e}")
            return False

        logger.info(f"原始数据行数: {len(df)}")

        # 检测字段映射
        mapping = self.detect_field_mapping(df)

        if not mapping:
            logger.warning("未检测到任何字段映射，将使用原始列名")

        # 过滤和验证数据
        valid_rows = []
        skipped_count = 0
        duplicate_skus = set()

        for index, row in df.iterrows():
            # 验证产品行
            is_valid, reason = self.validate_product_row(row, mapping)

            if not is_valid:
                skipped_count += 1
                logger.debug(f"跳过第 {index+1} 行: {reason}")
                continue

            # 检查SKU重复
            sku_col = mapping.get('sku')
            if sku_col and not pd.isna(row[sku_col]):
                sku = str(row[sku_col]).strip()
                if sku in duplicate_skus:
                    skipped_count += 1
                    logger.debug(f"跳过重复SKU第 {index+1} 行: {sku}")
                    continue
                duplicate_skus.add(sku)

            valid_rows.append(row)

        logger.info(f"有效产品数量: {len(valid_rows)}, 跳过: {skipped_count}")

        if not valid_rows:
            logger.error("没有有效的产品数据")
            return False

        # 分批处理，每个文件最多30000个产品
        total_files = (len(valid_rows) + max_products_per_file - 1) // max_products_per_file

        # 设置输出目录
        if output_dir is None:
            output_dir = os.path.dirname(input_file)

        base_filename = os.path.splitext(os.path.basename(input_file))[0]

        for batch_num in range(total_files):
            start_idx = batch_num * max_products_per_file
            end_idx = min((batch_num + 1) * max_products_per_file, len(valid_rows))
            batch_rows = valid_rows[start_idx:end_idx]

            # 创建WooCommerce格式的DataFrame
            wc_data = []

            for row in batch_rows:
                wc_row = self.create_woocommerce_row(row, mapping)
                wc_data.append(wc_row)

            # 创建DataFrame
            wc_df = pd.DataFrame(wc_data, columns=self.wc_columns)

            # 生成输出文件名
            if total_files > 1:
                output_filename = f"{base_filename}_wc_part{batch_num+1}.csv"
            else:
                output_filename = f"{base_filename}_wc.csv"

            output_path = os.path.join(output_dir, output_filename)

            # 保存CSV文件
            wc_df.to_csv(output_path, index=False, encoding='utf-8-sig')

            logger.info(f"已生成文件 {batch_num+1}/{total_files}: {output_filename} ({len(batch_rows)} 个产品)")

        logger.info(f"转换完成！总共生成 {total_files} 个文件")
        return True

    def create_woocommerce_row(self, row, mapping):
        """创建单个WooCommerce产品行"""
        wc_row = {}

        # 初始化所有列为空值
        for col in self.wc_columns:
            wc_row[col] = ""

        # 设置默认值
        for key, value in self.default_values.items():
            wc_row[key] = value

        # 映射基本字段
        if 'name' in mapping and not pd.isna(row[mapping['name']]):
            wc_row['Name'] = str(row[mapping['name']]).strip()

        if 'sku' in mapping and not pd.isna(row[mapping['sku']]):
            wc_row['SKU'] = str(row[mapping['sku']]).strip()

        # 处理描述
        if 'description' in mapping and not pd.isna(row[mapping['description']]):
            wc_row['Description'] = self.clean_html_description(row[mapping['description']])

        if 'short_description' in mapping and not pd.isna(row[mapping['short_description']]):
            wc_row['Short description'] = self.clean_html_description(row[mapping['short_description']])

        # 处理价格
        if 'regular_price' in mapping and not pd.isna(row[mapping['regular_price']]):
            price = str(row[mapping['regular_price']]).replace(',', '.')
            price = re.sub(r'[^\d.]', '', price)
            try:
                wc_row['Regular price'] = f"{float(price):.2f}"
            except:
                wc_row['Regular price'] = ""

        if 'sale_price' in mapping and not pd.isna(row[mapping['sale_price']]):
            price = str(row[mapping['sale_price']]).replace(',', '.')
            price = re.sub(r'[^\d.]', '', price)
            try:
                wc_row['Sale price'] = f"{float(price):.2f}"
            except:
                wc_row['Sale price'] = ""

        # 处理库存
        if 'stock' in mapping and not pd.isna(row[mapping['stock']]):
            try:
                stock = int(float(str(row[mapping['stock']]).replace(',', '.')))
                wc_row['Stock'] = str(stock)
            except:
                wc_row['Stock'] = ""

        # 处理分类和标签
        if 'categories' in mapping and not pd.isna(row[mapping['categories']]):
            wc_row['Categories'] = self.normalize_separators(row[mapping['categories']], 'categories')

        if 'tags' in mapping and not pd.isna(row[mapping['tags']]):
            wc_row['Tags'] = self.normalize_separators(row[mapping['tags']], 'tags')

        # 处理图片
        if 'images' in mapping and not pd.isna(row[mapping['images']]):
            wc_row['Images'] = self.normalize_separators(row[mapping['images']], 'images')

        # 处理品牌
        if 'brand' in mapping and not pd.isna(row[mapping['brand']]):
            wc_row['Brands'] = str(row[mapping['brand']]).strip()
            # 同时设置为属性
            wc_row['Attribute 1 name'] = 'Brand'
            wc_row['Attribute 1 value(s)'] = str(row[mapping['brand']]).strip()
            wc_row['Attribute 1 visible'] = '1'
            wc_row['Attribute 1 global'] = '0'

        # 处理尺寸和重量
        if 'weight' in mapping and not pd.isna(row[mapping['weight']]):
            try:
                weight = float(str(row[mapping['weight']]).replace(',', '.'))
                wc_row['Weight (kg)'] = f"{weight:.2f}"
            except:
                pass

        if 'length' in mapping and not pd.isna(row[mapping['length']]):
            try:
                length = float(str(row[mapping['length']]).replace(',', '.'))
                wc_row['Length (cm)'] = f"{length:.2f}"
            except:
                pass

        if 'width' in mapping and not pd.isna(row[mapping['width']]):
            try:
                width = float(str(row[mapping['width']]).replace(',', '.'))
                wc_row['Width (cm)'] = f"{width:.2f}"
            except:
                pass

        if 'height' in mapping and not pd.isna(row[mapping['height']]):
            try:
                height = float(str(row[mapping['height']]).replace(',', '.'))
                wc_row['Height (cm)'] = f"{height:.2f}"
            except:
                pass

        return wc_row

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='WooCommerce产品数据转换工具')
    parser.add_argument('input_file', help='输入文件路径')
    parser.add_argument('-o', '--output', help='输出目录路径', default=None)
    parser.add_argument('-m', '--max-products', type=int, default=30000,
                       help='每个CSV文件的最大产品数量 (默认: 30000)')
    parser.add_argument('-v', '--verbose', action='store_true', help='详细输出')

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # 检查输入文件是否存在
    if not os.path.exists(args.input_file):
        logger.error(f"输入文件不存在: {args.input_file}")
        return False

    # 创建转换器实例
    converter = WooCommerceConverter()

    # 执行转换
    success = converter.convert_to_woocommerce(
        args.input_file,
        args.output,
        args.max_products
    )

    if success:
        logger.info("转换成功完成！")
        return True
    else:
        logger.error("转换失败！")
        return False

def batch_convert_directory(directory_path, output_dir=None, max_products_per_file=30000):
    """批量转换目录中的所有文件"""
    converter = WooCommerceConverter()

    # 支持的文件扩展名
    supported_extensions = ['.xlsx', '.xls', '.csv']

    # 查找所有支持的文件
    files_to_convert = []
    for root, dirs, files in os.walk(directory_path):
        for file in files:
            if any(file.lower().endswith(ext) for ext in supported_extensions):
                # 跳过临时文件
                if not file.startswith('~$'):
                    files_to_convert.append(os.path.join(root, file))

    logger.info(f"找到 {len(files_to_convert)} 个文件需要转换")

    success_count = 0
    failed_files = []

    for file_path in files_to_convert:
        logger.info(f"正在处理: {file_path}")
        try:
            # 设置输出目录
            if output_dir:
                file_output_dir = output_dir
            else:
                file_output_dir = os.path.dirname(file_path)

            success = converter.convert_to_woocommerce(
                file_path,
                file_output_dir,
                max_products_per_file
            )

            if success:
                success_count += 1
                logger.info(f"✓ 成功转换: {file_path}")
            else:
                failed_files.append(file_path)
                logger.error(f"✗ 转换失败: {file_path}")

        except Exception as e:
            failed_files.append(file_path)
            logger.error(f"✗ 转换出错: {file_path} - {e}")

    logger.info(f"批量转换完成！成功: {success_count}, 失败: {len(failed_files)}")

    if failed_files:
        logger.info("失败的文件:")
        for file_path in failed_files:
            logger.info(f"  - {file_path}")

    return success_count, failed_files

if __name__ == "__main__":
    # 如果没有命令行参数，则批量处理当前目录
    if len(sys.argv) == 1:
        current_dir = os.getcwd()
        logger.info(f"批量处理目录: {current_dir}")
        batch_convert_directory(current_dir)
    else:
        main()
