#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WooCommerce产品数据转换工具
智能转换各种格式的产品数据到WooCommerce兼容的CSV格式
"""

import pandas as pd
import re
import os
import sys
from pathlib import Path
import html
from bs4 import BeautifulSoup
import argparse
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WooCommerceConverter:
    def __init__(self):
        # WooCommerce标准字段映射
        self.wc_columns = [
            'ID', 'Type', 'SKU', 'Name', 'Published', 'Is featured?', 'Visibility in catalog',
            'Short description', 'Description', 'Date sale price starts', 'Date sale price ends',
            'Tax status', 'Tax class', 'In stock?', 'Stock', 'Low stock amount', 'Backorders allowed?',
            'Sold individually?', 'Weight (kg)', 'Length (cm)', 'Width (cm)', 'Height (cm)',
            'Allow customer reviews?', 'Purchase note', 'Sale price', 'Regular price', 'Categories',
            'Tags', 'Shipping class', 'Images', 'Download limit', 'Download expiry days', 'Parent',
            'Grouped products', 'Upsells', 'Cross-sells', 'External URL', 'Button text', 'Position',
            'Attribute 1 name', 'Attribute 1 value(s)', 'Attribute 1 visible', 'Attribute 1 global',
            'Attribute 2 name', 'Attribute 2 value(s)', 'Attribute 2 visible', 'Attribute 2 global',
            'Attribute 3 name', 'Attribute 3 value(s)', 'Attribute 3 visible', 'Attribute 3 global',
            'Attribute 4 name', 'Attribute 4 value(s)', 'Attribute 4 visible', 'Attribute 4 global',
            'Brands', 'Meta: rank_math_focus_keyword'
        ]
        
        # 常见字段映射规则
        self.field_mappings = {
            # 产品名称相关
            'name': ['name', 'title', 'product_name', 'nombre', 'nome', 'titel', 'nom'],
            'description': ['description', 'desc', 'descripcion', 'descrizione', 'beschreibung'],
            'short_description': ['short_description', 'short_desc', 'summary', 'resumen'],
            
            # 价格相关
            'regular_price': ['price', 'regular_price', 'precio', 'prezzo', 'preis', 'prix'],
            'sale_price': ['sale_price', 'offer_price', 'precio_oferta', 'prezzo_scontato'],
            
            # 库存相关
            'stock': ['stock', 'quantity', 'qty', 'cantidad', 'quantita', 'menge'],
            'sku': ['sku', 'code', 'codigo', 'codice', 'artikel'],
            
            # 分类和标签
            'categories': ['category', 'categories', 'categoria', 'categorie', 'kategorie'],
            'tags': ['tags', 'etiquetas', 'tag', 'schlagworte'],
            
            # 图片
            'images': ['image', 'images', 'imagen', 'immagine', 'bild', 'photo'],
            
            # 品牌
            'brand': ['brand', 'marca', 'marque', 'marke'],
            
            # 重量和尺寸
            'weight': ['weight', 'peso', 'gewicht', 'poids'],
            'length': ['length', 'largo', 'lunghezza', 'lange'],
            'width': ['width', 'ancho', 'larghezza', 'breite'],
            'height': ['height', 'alto', 'altezza', 'hohe']
        }
        
    def clean_html_description(self, text):
        """清理和优化HTML描述"""
        if pd.isna(text) or not text:
            return ""
            
        # 转换为字符串
        text = str(text)
        
        # 解码HTML实体
        text = html.unescape(text)
        
        # 使用BeautifulSoup清理HTML
        soup = BeautifulSoup(text, 'html.parser')
        
        # 移除不需要的标签
        for tag in soup(['script', 'style', 'meta', 'link']):
            tag.decompose()
            
        # 转换常见标签为WooCommerce兼容格式
        tag_replacements = {
            'b': 'strong',
            'i': 'em',
            'u': 'span style="text-decoration: underline;"'
        }
        
        for old_tag, new_tag in tag_replacements.items():
            for tag in soup.find_all(old_tag):
                tag.name = new_tag.split()[0] if ' ' in new_tag else new_tag
                if ' ' in new_tag:
                    attrs = new_tag.split(' ', 1)[1]
                    # 简单解析属性
                    if 'style=' in attrs:
                        style_value = attrs.split('style="')[1].split('"')[0]
                        tag['style'] = style_value
        
        # 清理空白和格式
        cleaned_text = str(soup)
        cleaned_text = re.sub(r'\s+', ' ', cleaned_text)
        cleaned_text = cleaned_text.strip()
        
        return cleaned_text
    
    def normalize_separators(self, text, target_separator='|'):
        """标准化分隔符"""
        if pd.isna(text) or not text:
            return ""
            
        text = str(text)
        
        # 常见分隔符列表
        separators = [',', ';', '/', '\\', '|', '>', '<', '&gt;', '&lt;']
        
        # 替换所有分隔符为目标分隔符
        for sep in separators:
            if sep != target_separator:
                text = text.replace(sep, target_separator)
        
        # 清理多余的分隔符
        text = re.sub(f'\\{target_separator}+', target_separator, text)
        text = text.strip(target_separator)
        
        return text
    
    def detect_field_mapping(self, df):
        """智能检测字段映射"""
        columns = [col.lower().strip() for col in df.columns]
        mapping = {}
        
        for wc_field, possible_names in self.field_mappings.items():
            for col in columns:
                for possible_name in possible_names:
                    if possible_name.lower() in col or col in possible_name.lower():
                        original_col = df.columns[columns.index(col)]
                        mapping[wc_field] = original_col
                        break
                if wc_field in mapping:
                    break
        
        logger.info(f"检测到的字段映射: {mapping}")
        return mapping
    
    def validate_product_row(self, row, mapping):
        """验证产品行是否完整"""
        # 必需字段检查
        required_fields = ['name', 'regular_price']
        
        for field in required_fields:
            if field in mapping:
                col = mapping[field]
                if pd.isna(row[col]) or not str(row[col]).strip():
                    return False, f"缺少必需字段: {field}"
        
        # 价格验证
        if 'regular_price' in mapping:
            try:
                price = str(row[mapping['regular_price']]).replace(',', '.')
                price = re.sub(r'[^\d.]', '', price)
                if not price or float(price) <= 0:
                    return False, "价格无效"
            except:
                return False, "价格格式错误"
        
        return True, "有效"
