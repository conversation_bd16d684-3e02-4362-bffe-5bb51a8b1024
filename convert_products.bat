@echo off
chcp 65001 >nul
echo ========================================
echo WooCommerce产品数据转换工具
echo ========================================
echo.

REM 检查Python是否安装
py --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

REM 安装必要的依赖包
echo 正在检查和安装必要的依赖包...
py -m pip install pandas openpyxl beautifulsoup4 lxml --quiet

echo.
echo 开始批量转换产品数据...
echo 每个CSV文件最多包含30,000个产品
echo.

REM 运行转换脚本
py product_converter.py

echo.
echo 转换完成！请检查生成的 *_wc.csv 或 *_wc_part*.csv 文件
echo.
pause
