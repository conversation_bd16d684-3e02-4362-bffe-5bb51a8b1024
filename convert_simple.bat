@echo off
chcp 65001 >nul
echo ========================================
echo WooCommerce产品数据转换工具 (简化版)
echo ========================================
echo.

REM 检查Python是否安装
py --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

echo Python版本:
py --version

echo.
echo 正在检查openpyxl库...
py -c "import openpyxl; print('openpyxl已安装')" 2>nul
if errorlevel 1 (
    echo 正在安装openpyxl库...
    py -m pip install openpyxl
    if errorlevel 1 (
        echo 警告: openpyxl安装失败，将无法处理Excel文件
        echo 但仍可以处理CSV文件
    )
)

echo.
echo 开始批量转换产品数据...
echo 每个CSV文件最多包含30,000个产品
echo 支持的文件格式: .xlsx, .xls, .csv
echo.

REM 运行转换脚本
py simple_converter.py

echo.
echo 转换完成！请检查生成的 *_wc.csv 或 *_wc_part*.csv 文件
echo.
pause
