@echo off
chcp 65001 >nul
echo ========================================
echo WooCommerce产品数据转换工具 (仅CSV版本)
echo ========================================
echo.

REM 检查Python是否安装
py --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

echo Python版本:
py --version

echo.
echo 注意: 此版本只能处理CSV文件
echo 如果您有Excel文件(.xlsx/.xls)，请先转换为CSV格式：
echo 1. 在Excel中打开文件
echo 2. 文件 -^> 另存为 -^> CSV UTF-8 (逗号分隔)
echo 3. 保存后再运行此工具
echo.

echo 开始批量转换CSV文件...
echo 每个CSV文件最多包含30,000个产品
echo.

REM 运行转换脚本
py csv_only_converter.py

echo.
echo 转换完成！请检查生成的 *_wc.csv 或 *_wc_part*.csv 文件
echo.
pause
