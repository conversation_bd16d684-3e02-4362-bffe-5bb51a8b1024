#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
仅处理CSV文件的WooCommerce产品数据转换工具
不依赖任何第三方库，只使用Python标准库
"""

import csv
import os
import sys
import re
import html

class CSVWooCommerceConverter:
    def __init__(self):
        # WooCommerce标准字段
        self.wc_columns = [
            'ID', 'Type', 'SKU', 'Name', 'Published', 'Is featured?', 'Visibility in catalog',
            'Short description', 'Description', 'Date sale price starts', 'Date sale price ends',
            'Tax status', 'Tax class', 'In stock?', 'Stock', 'Low stock amount', 'Backorders allowed?',
            'Sold individually?', 'Weight (kg)', 'Length (cm)', 'Width (cm)', 'Height (cm)',
            'Allow customer reviews?', 'Purchase note', 'Sale price', 'Regular price', 'Categories',
            'Tags', 'Shipping class', 'Images', 'Download limit', 'Download expiry days', 'Parent',
            'Grouped products', 'Upsells', 'Cross-sells', 'External URL', 'Button text', 'Position',
            'Attribute 1 name', 'Attribute 1 value(s)', 'Attribute 1 visible', 'Attribute 1 global',
            'Attribute 2 name', 'Attribute 2 value(s)', 'Attribute 2 visible', 'Attribute 2 global',
            'Attribute 3 name', 'Attribute 3 value(s)', 'Attribute 3 visible', 'Attribute 3 global',
            'Attribute 4 name', 'Attribute 4 value(s)', 'Attribute 4 visible', 'Attribute 4 global',
            'Brands', 'Meta: rank_math_focus_keyword'
        ]
        
        # 字段映射（支持多语言）
        self.field_mappings = {
            'name': ['name', 'title', 'product_name', 'nombre', 'nome', 'titel', 'nom', 
                    '产品名称', '商品名称', '标题', '名称', 'producto', 'prodotto'],
            'description': ['description', 'desc', 'descripcion', 'descrizione', 'beschreibung', 
                           '描述', '详细描述', '产品描述', 'detalle', 'dettaglio'],
            'short_description': ['short_description', 'short_desc', 'summary', 'resumen', 
                                 '简短描述', '摘要', 'breve'],
            'regular_price': ['price', 'regular_price', 'precio', 'prezzo', 'preis', 'prix', 
                             '价格', '原价', '售价', 'cost', 'costo'],
            'sale_price': ['sale_price', 'offer_price', 'precio_oferta', 'prezzo_scontato', 
                          '促销价', '特价', 'oferta'],
            'stock': ['stock', 'quantity', 'qty', 'cantidad', 'quantita', 'menge', 
                     '库存', '数量', 'inventario'],
            'sku': ['sku', 'code', 'codigo', 'codice', 'artikel', 
                   '编码', '商品编码', 'ref', 'referencia'],
            'categories': ['category', 'categories', 'categoria', 'categorie', 'kategorie', 
                          '分类', '类别', 'cat'],
            'tags': ['tags', 'etiquetas', 'tag', 'schlagworte', 
                    '标签', '关键词', 'palabras'],
            'images': ['image', 'images', 'imagen', 'immagine', 'bild', 'photo', 
                      '图片', '图像', 'foto'],
            'brand': ['brand', 'marca', 'marque', 'marke', 
                     '品牌', '厂商', 'fabricante'],
            'weight': ['weight', 'peso', 'gewicht', 'poids', '重量'],
            'length': ['length', 'largo', 'lunghezza', 'lange', '长度'],
            'width': ['width', 'ancho', 'larghezza', 'breite', '宽度'],
            'height': ['height', 'alto', 'altezza', 'hohe', '高度']
        }
    
    def clean_html_description(self, text):
        """清理HTML描述"""
        if not text or text.strip() == '':
            return ""
        
        text = str(text)
        
        # 解码HTML实体
        text = html.unescape(text)
        
        # 简单的HTML标签清理
        text = re.sub(r'<script[^>]*>.*?</script>', '', text, flags=re.DOTALL | re.IGNORECASE)
        text = re.sub(r'<style[^>]*>.*?</style>', '', text, flags=re.DOTALL | re.IGNORECASE)
        
        # 转换常见标签
        text = re.sub(r'<b\b[^>]*>', '<strong>', text, flags=re.IGNORECASE)
        text = re.sub(r'</b>', '</strong>', text, flags=re.IGNORECASE)
        text = re.sub(r'<i\b[^>]*>', '<em>', text, flags=re.IGNORECASE)
        text = re.sub(r'</i>', '</em>', text, flags=re.IGNORECASE)
        
        # 清理多余空白
        text = re.sub(r'\s+', ' ', text)
        text = text.strip()
        
        return text
    
    def normalize_separators(self, text, field_type):
        """标准化分隔符"""
        if not text or text.strip() == '':
            return ""

        text = str(text)

        # 根据字段类型设置目标分隔符
        if field_type == 'categories':
            target_sep = ' > '
        elif field_type in ['tags', 'images']:
            target_sep = ', '
        else:
            target_sep = ', '

        # 特殊处理图片字段，保护URL
        if field_type == 'images':
            # 对于图片，只处理明确的分隔符，不破坏URL
            # 先处理分号和管道符
            text = text.replace(';', ',')
            text = text.replace('|', ',')
            text = text.replace('\n', ',')
            text = text.replace('\r', ',')

            # 清理多余的逗号和空格，确保URL完整
            # 分割成单独的URL
            urls = [url.strip() for url in text.split(',') if url.strip()]

            # 重新组合，用逗号和空格分隔
            text = ', '.join(urls)

            return text

        # 对于其他字段的常见分隔符列表
        separators = [';', '/', '\\', '|', '>', '<', '&gt;', '&lt;', ' > ', ' / ', ' | ', '\n', '\r']

        # 替换分隔符
        for sep in separators:
            if sep != target_sep:
                text = text.replace(sep, target_sep)

        # 处理逗号分隔符（只有当不包含URL时）
        if 'http' not in text.lower():
            text = text.replace(',', target_sep)

        # 清理多余的分隔符
        if target_sep == ' > ':
            text = re.sub(r'\s*>\s*', ' > ', text)
            text = re.sub(r'(\s*>\s*)+', ' > ', text)
        elif target_sep == ', ':
            text = re.sub(r'\s*,\s*', ', ', text)
            text = re.sub(r'(,\s*)+', ', ', text)

        text = text.strip().strip(target_sep.strip())
        return text
    
    def detect_field_mapping(self, headers):
        """检测字段映射"""
        headers_lower = [h.lower().strip() for h in headers]
        mapping = {}
        
        # 跳过已经是WooCommerce格式的文件
        wc_headers = [col.lower() for col in self.wc_columns]
        if len([h for h in headers_lower if h in wc_headers]) > 10:
            print("检测到这是WooCommerce格式的文件，跳过转换")
            return {}
        
        for wc_field, possible_names in self.field_mappings.items():
            for i, header in enumerate(headers_lower):
                for possible_name in possible_names:
                    # 更精确的匹配
                    if (possible_name.lower() == header or 
                        (possible_name.lower() in header and len(possible_name) > 3) or
                        (header in possible_name.lower() and len(header) > 3)):
                        mapping[wc_field] = i
                        break
                if wc_field in mapping:
                    break
        
        print(f"检测到的字段映射:")
        for wc_field, col_index in mapping.items():
            print(f"  {wc_field} -> 第{col_index+1}列: {headers[col_index]}")
        
        return mapping
    
    def validate_product_row(self, row, mapping):
        """验证产品行"""
        # 检查必需字段
        if 'name' in mapping:
            name = row[mapping['name']] if mapping['name'] < len(row) else ""
            if not name or name.strip() == "":
                return False, "缺少产品名称"
        else:
            return False, "未找到产品名称字段"
        
        if 'regular_price' in mapping:
            price = row[mapping['regular_price']] if mapping['regular_price'] < len(row) else ""
            if not price or price.strip() == "":
                return False, "缺少价格"
            
            # 验证价格格式
            try:
                price_clean = re.sub(r'[^\d.,]', '', str(price)).replace(',', '.')
                if not price_clean or float(price_clean) <= 0:
                    return False, "价格无效"
            except:
                return False, "价格格式错误"
        
        return True, "有效"
    
    def create_woocommerce_row(self, row, mapping):
        """创建WooCommerce格式的行"""
        wc_row = [""] * len(self.wc_columns)
        
        # 设置默认值
        wc_row[self.wc_columns.index('Type')] = 'simple'
        wc_row[self.wc_columns.index('Published')] = '1'
        wc_row[self.wc_columns.index('Is featured?')] = '0'
        wc_row[self.wc_columns.index('Visibility in catalog')] = 'visible'
        wc_row[self.wc_columns.index('Tax status')] = 'taxable'
        wc_row[self.wc_columns.index('In stock?')] = '1'
        wc_row[self.wc_columns.index('Backorders allowed?')] = '0'
        wc_row[self.wc_columns.index('Sold individually?')] = '0'
        wc_row[self.wc_columns.index('Allow customer reviews?')] = '1'
        wc_row[self.wc_columns.index('Position')] = '0'
        
        # 映射字段
        for wc_field, col_index in mapping.items():
            if col_index >= len(row):
                continue
                
            value = row[col_index] if row[col_index] else ""
            
            if wc_field == 'name':
                wc_row[self.wc_columns.index('Name')] = str(value).strip()
            elif wc_field == 'sku':
                wc_row[self.wc_columns.index('SKU')] = str(value).strip()
            elif wc_field == 'description':
                wc_row[self.wc_columns.index('Description')] = self.clean_html_description(value)
            elif wc_field == 'short_description':
                wc_row[self.wc_columns.index('Short description')] = self.clean_html_description(value)
            elif wc_field == 'regular_price':
                price = re.sub(r'[^\d.,]', '', str(value)).replace(',', '.')
                try:
                    wc_row[self.wc_columns.index('Regular price')] = f"{float(price):.2f}"
                except:
                    pass
            elif wc_field == 'sale_price':
                price = re.sub(r'[^\d.,]', '', str(value)).replace(',', '.')
                try:
                    wc_row[self.wc_columns.index('Sale price')] = f"{float(price):.2f}"
                except:
                    pass
            elif wc_field == 'stock':
                try:
                    wc_row[self.wc_columns.index('Stock')] = str(int(float(str(value).replace(',', '.'))))
                except:
                    pass
            elif wc_field == 'categories':
                wc_row[self.wc_columns.index('Categories')] = self.normalize_separators(value, 'categories')
            elif wc_field == 'tags':
                wc_row[self.wc_columns.index('Tags')] = self.normalize_separators(value, 'tags')
            elif wc_field == 'images':
                wc_row[self.wc_columns.index('Images')] = self.normalize_separators(value, 'images')
            elif wc_field == 'brand':
                brand_value = str(value).strip()
                wc_row[self.wc_columns.index('Brands')] = brand_value
                # 设置为属性
                wc_row[self.wc_columns.index('Attribute 1 name')] = 'Brand'
                wc_row[self.wc_columns.index('Attribute 1 value(s)')] = brand_value
                wc_row[self.wc_columns.index('Attribute 1 visible')] = '1'
                wc_row[self.wc_columns.index('Attribute 1 global')] = '0'
        
        return wc_row
    
    def read_csv_file(self, file_path):
        """读取CSV文件"""
        encodings = ['utf-8', 'utf-8-sig', 'gbk', 'gb2312', 'latin1', 'cp1252']
        
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding, newline='') as f:
                    # 检测分隔符
                    sample = f.read(1024)
                    f.seek(0)
                    
                    delimiter = ','
                    if sample.count(';') > sample.count(','):
                        delimiter = ';'
                    elif sample.count('\t') > sample.count(','):
                        delimiter = '\t'
                    
                    reader = csv.reader(f, delimiter=delimiter)
                    rows = list(reader)
                    
                    print(f"成功使用编码 {encoding} 读取CSV文件")
                    return rows
                    
            except Exception as e:
                continue
        
        raise Exception("无法读取CSV文件，请检查编码格式")
    
    def convert_file(self, input_file, output_dir=None, max_products_per_file=30000):
        """转换单个CSV文件"""
        print(f"\n开始处理文件: {input_file}")
        
        if not input_file.lower().endswith('.csv'):
            print(f"此工具只支持CSV文件，请先将Excel文件转换为CSV格式")
            return False
        
        # 读取文件
        try:
            rows = self.read_csv_file(input_file)
        except Exception as e:
            print(f"读取文件失败: {e}")
            return False
        
        if len(rows) < 2:
            print("文件数据不足")
            return False
        
        # 获取表头
        headers = rows[0]
        data_rows = rows[1:]
        
        print(f"原始数据: {len(data_rows)} 行")
        
        # 检测字段映射
        mapping = self.detect_field_mapping(headers)
        
        if not mapping:
            print("警告: 未检测到任何字段映射，可能需要手动调整字段名称")
            return False
        
        # 过滤和验证数据
        valid_rows = []
        skipped_count = 0
        duplicate_skus = set()
        
        for i, row in enumerate(data_rows):
            # 验证行
            is_valid, reason = self.validate_product_row(row, mapping)
            
            if not is_valid:
                skipped_count += 1
                if i < 10:  # 只显示前10个错误
                    print(f"跳过第 {i+2} 行: {reason}")
                continue
            
            # 检查SKU重复
            if 'sku' in mapping and mapping['sku'] < len(row):
                sku = str(row[mapping['sku']]).strip()
                if sku and sku in duplicate_skus:
                    skipped_count += 1
                    if i < 10:
                        print(f"跳过重复SKU第 {i+2} 行: {sku}")
                    continue
                if sku:
                    duplicate_skus.add(sku)
            
            valid_rows.append(row)
        
        print(f"有效产品: {len(valid_rows)} 个，跳过: {skipped_count} 个")
        
        if not valid_rows:
            print("没有有效的产品数据")
            return False
        
        # 设置输出目录
        if output_dir is None:
            output_dir = os.path.dirname(input_file)
        
        base_filename = os.path.splitext(os.path.basename(input_file))[0]
        
        # 分批处理
        total_files = (len(valid_rows) + max_products_per_file - 1) // max_products_per_file
        
        for batch_num in range(total_files):
            start_idx = batch_num * max_products_per_file
            end_idx = min((batch_num + 1) * max_products_per_file, len(valid_rows))
            batch_rows = valid_rows[start_idx:end_idx]
            
            # 创建WooCommerce数据
            wc_data = []
            for row in batch_rows:
                wc_row = self.create_woocommerce_row(row, mapping)
                wc_data.append(wc_row)
            
            # 生成输出文件名
            if total_files > 1:
                output_filename = f"{base_filename}_wc_part{batch_num+1}.csv"
            else:
                output_filename = f"{base_filename}_wc.csv"
            
            output_path = os.path.join(output_dir, output_filename)
            
            # 保存CSV文件
            with open(output_path, 'w', encoding='utf-8-sig', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(self.wc_columns)  # 写入表头
                writer.writerows(wc_data)  # 写入数据
            
            print(f"✓ 已生成文件 {batch_num+1}/{total_files}: {output_filename} ({len(batch_rows)} 个产品)")
        
        print(f"转换完成！总共生成 {total_files} 个文件")
        return True

def batch_convert_directory(directory_path, max_products_per_file=30000):
    """批量转换目录中的所有CSV文件"""
    converter = CSVWooCommerceConverter()
    
    # 查找所有CSV文件
    csv_files = []
    for root, dirs, files in os.walk(directory_path):
        for file in files:
            if file.lower().endswith('.csv') and '_wc' not in file:
                csv_files.append(os.path.join(root, file))
    
    print(f"找到 {len(csv_files)} 个CSV文件需要转换")
    
    if not csv_files:
        print("没有找到CSV文件。")
        print("如果您有Excel文件，请先将它们转换为CSV格式。")
        print("在Excel中：文件 -> 另存为 -> CSV UTF-8 (逗号分隔)")
        return
    
    success_count = 0
    failed_files = []
    
    for file_path in csv_files:
        try:
            success = converter.convert_file(file_path, None, max_products_per_file)
            
            if success:
                success_count += 1
                print(f"✓ 成功转换: {os.path.relpath(file_path, directory_path)}")
            else:
                failed_files.append(file_path)
                print(f"✗ 转换失败: {os.path.relpath(file_path, directory_path)}")
                
        except Exception as e:
            failed_files.append(file_path)
            print(f"✗ 转换出错: {os.path.relpath(file_path, directory_path)} - {e}")
    
    print(f"\n批量转换完成！")
    print(f"成功: {success_count} 个文件")
    print(f"失败: {len(failed_files)} 个文件")

if __name__ == "__main__":
    print("WooCommerce产品数据转换工具 (仅CSV)")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        # 命令行模式
        input_file = sys.argv[1]
        converter = CSVWooCommerceConverter()
        converter.convert_file(input_file)
    else:
        # 批量处理当前目录
        current_dir = os.getcwd()
        print(f"批量处理目录: {current_dir}")
        batch_convert_directory(current_dir)
