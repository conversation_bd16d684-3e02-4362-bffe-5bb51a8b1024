@echo off
chcp 65001 >nul
echo ========================================
echo WooCommerce产品数据转换工具 (完整版)
echo ========================================
echo.

REM 检查Python是否安装
py --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

echo Python版本:
py --version
echo.

REM 检查是否有Excel文件
set "hasExcel=false"
for /r %%f in (*.xlsx *.xls) do (
    if not "%%~nf"=="~$*" (
        set "hasExcel=true"
        goto :checkDone
    )
)
:checkDone

if "%hasExcel%"=="true" (
    echo 检测到Excel文件，正在转换为CSV格式...
    echo.
    
    REM 运行PowerShell脚本转换Excel到CSV
    powershell -ExecutionPolicy Bypass -File "excel_to_csv.ps1"
    
    if errorlevel 1 (
        echo.
        echo Excel转CSV失败，将只处理现有的CSV文件
        echo.
    ) else (
        echo.
        echo Excel转CSV完成！
        echo.
    )
) else (
    echo 未检测到Excel文件，将直接处理CSV文件
    echo.
)

echo 开始转换CSV文件为WooCommerce格式...
echo 每个CSV文件最多包含30,000个产品
echo.

REM 运行CSV转换脚本
py csv_only_converter.py

echo.
echo 全部转换完成！
echo 请检查生成的 *_wc.csv 或 *_wc_part*.csv 文件
echo.
pause
