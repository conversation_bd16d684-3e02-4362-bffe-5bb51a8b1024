# WooCommerce产品数据转换工具

这是一个智能的产品数据转换工具，可以将各种格式的产品数据转换成WooCommerce兼容的CSV格式。

## 🚀 快速开始

### 方法1: 一键转换（推荐）
1. 双击运行 `convert_all.bat`
2. 工具会自动：
   - 将Excel文件转换为CSV格式
   - 转换CSV文件为WooCommerce格式
   - 每个文件最多包含30,000个产品

### 方法2: 仅转换CSV文件
1. 先将Excel文件手动转换为CSV格式
2. 双击运行 `convert_csv_only.bat`

### 方法3: 仅转换Excel为CSV
1. 双击运行 `excel_to_csv.ps1`（需要安装Excel）

## 主要功能

1. **智能字段映射** - 自动识别不同语言和格式的字段名称
2. **HTML标签优化** - 清理和转换HTML描述为WooCommerce兼容格式
3. **分隔符标准化** - 统一分类、标签、图片等字段的分隔符
4. **数据验证** - 跳过不完整或重复的产品数据
5. **批量处理** - 支持批量转换多个文件
6. **分文件输出** - 每个CSV文件最多包含30,000个产品

## 支持的文件格式

- Excel文件 (.xlsx, .xls)
- CSV文件 (.csv)

## 使用方法

### 方法1: 简单批处理（推荐）

1. 将所有需要转换的文件放在同一目录下
2. 双击运行 `convert_products.bat`
3. 等待转换完成

### 方法2: 命令行使用

```bash
# 转换单个文件
python product_converter.py input_file.xlsx

# 指定输出目录
python product_converter.py input_file.xlsx -o output_directory

# 自定义每个文件的最大产品数量
python product_converter.py input_file.xlsx -m 25000

# 详细输出模式
python product_converter.py input_file.xlsx -v
```

### 方法3: 批量转换目录

```python
from product_converter import batch_convert_directory

# 转换指定目录下的所有文件
batch_convert_directory('d:/wc-data', output_dir='d:/output', max_products_per_file=30000)
```

## 输出文件命名规则

- 单个文件: `原文件名_wc.csv`
- 多个文件: `原文件名_wc_part1.csv`, `原文件名_wc_part2.csv`, ...

## 字段映射配置

工具会自动识别以下字段（支持多语言）：

| WooCommerce字段 | 识别的字段名称 |
|----------------|---------------|
| Name | name, title, product_name, nombre, nome, 产品名称 |
| Description | description, desc, descripcion, 描述 |
| Regular price | price, regular_price, precio, 价格 |
| Categories | category, categories, categoria, 分类 |
| Tags | tags, etiquetas, tag, 标签 |
| Images | image, images, imagen, 图片 |
| Brand | brand, marca, marque, 品牌 |
| SKU | sku, code, codigo, 编码 |
| Stock | stock, quantity, qty, 库存 |

## 自定义配置

可以通过修改 `field_mapping_config.json` 文件来自定义：

- 字段映射规则
- 分隔符设置
- HTML标签替换规则
- 必需字段
- 默认值

## 数据验证规则

工具会自动跳过以下情况的产品：

1. 缺少必需字段（产品名称、价格）
2. 价格格式错误或为0
3. SKU重复的产品
4. 完全空白的行

## HTML描述处理

- 自动清理无用的HTML标签
- 转换标签为WooCommerce兼容格式
- 解码HTML实体
- 优化格式和空白

## 分隔符处理

- 分类: 使用 ` > ` 分隔（如：`电子产品 > 手机 > 智能手机`）
- 标签: 使用 `, ` 分隔（如：`热销, 新品, 推荐`）
- 图片: 使用 `, ` 分隔多个图片URL

## 依赖包

工具会自动安装以下依赖包：

- pandas - 数据处理
- openpyxl - Excel文件读取
- beautifulsoup4 - HTML处理
- lxml - XML解析

## 注意事项

1. 确保输入文件编码正确（支持UTF-8、GBK等）
2. 图片URL应该是完整的网址
3. 价格字段应该是数字格式
4. 分类层级建议不超过4级
5. 每个CSV文件最多30,000个产品，超出会自动分割

## 故障排除

### 常见问题

1. **编码错误**: 工具会自动尝试多种编码格式
2. **字段识别失败**: 检查字段名称是否在配置文件中
3. **价格格式错误**: 确保价格字段只包含数字和小数点
4. **图片链接无效**: 检查图片URL是否完整且可访问

### 日志输出

运行时会显示详细的处理信息：
- 检测到的字段映射
- 处理的产品数量
- 跳过的产品及原因
- 生成的文件信息

## 示例

假设有一个包含以下列的Excel文件：
- 产品名称
- 描述
- 价格
- 分类
- 图片

工具会自动：
1. 识别字段映射
2. 清理HTML描述
3. 标准化分类分隔符
4. 验证价格格式
5. 生成WooCommerce兼容的CSV文件

转换后的文件可以直接导入到WooCommerce中使用。
